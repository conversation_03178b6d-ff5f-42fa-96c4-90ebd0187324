@font-face {
  font-family: "iconfont"; /* Project id 4177535 */
  src: url('iconfont.woff2?t=1695973049779') format('woff2'),
       url('iconfont.woff?t=1695973049779') format('woff'),
       url('iconfont.ttf?t=1695973049779') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-linkedin:before {
  content: "\e890";
}

.icon-google:before {
  content: "\e87a";
}

.icon-link:before {
  content: "\e7e2";
}

.icon-facebook:before {
  content: "\e87e";
}

.icon-twitter:before {
  content: "\e882";
}

.icon-weibo:before {
  content: "\e883";
}

.icon-github-fill:before {
  content: "\e885";
}

