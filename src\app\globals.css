@tailwind base;
@tailwind components;
@tailwind utilities;

@import '../icon/iconfont.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 全局滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Webkit浏览器滚动条样式 */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

*::-webkit-scrollbar-corner {
  background: transparent;
}

/* 暗色模式下的滚动条 */
@media (prefers-color-scheme: dark) {
  * {
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
  }

  *::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.5);
  }

  *::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.8);
  }
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 保持原有的overflow-x设置 */
body {
  overflow-x: hidden; /* 防止水平滚动条 */
}

/* 优化选择文本的样式 */
::selection {
  background: rgba(59, 130, 246, 0.3);
  color: inherit;
}

::-moz-selection {
  background: rgba(59, 130, 246, 0.3);
  color: inherit;
}

/* 优化焦点样式 */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* 优化按钮和链接的过渡效果 */
button, a, input, textarea, select {
  transition: all 0.2s ease;
}

/* 优化卡片阴影 */
.card, .shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.card:hover, .shadow:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 优化加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 优化思维导图容器 */
.mind-elixir-container {
  border-radius: 8px;
  /* overflow: hidden; */
}

/* 思维导图卡片预览 - 完全禁用滚动和交互 */
.card .mind-elixir-container,
.card .mind-elixir-container *,
.card figure .mind-elixir-container,
.card figure .mind-elixir-container * {
  overflow: hidden !important;
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 隐藏思维导图卡片中的所有滚动条 */
.card .mind-elixir-container::-webkit-scrollbar,
.card .mind-elixir-container *::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.card .mind-elixir-container,
.card .mind-elixir-container * {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* 优化下拉菜单 */
.dropdown-content {
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
  .dropdown-content {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* 优雅的导航栏hover效果 */
.navbar-item {
  @apply transition-all duration-200 ease-in-out;
}

.navbar-item:hover {
  @apply bg-slate-50 text-slate-900;
}

@media (prefers-color-scheme: dark) {
  .navbar-item:hover {
    @apply bg-slate-800 text-slate-100;
  }
}

/* 优化按钮hover效果 */
.btn-elegant:hover {
  @apply bg-slate-100 text-slate-900 shadow-sm;
  transform: translateY(-1px);
}

@media (prefers-color-scheme: dark) {
  .btn-elegant:hover {
    @apply bg-slate-700 text-slate-100;
  }
}
